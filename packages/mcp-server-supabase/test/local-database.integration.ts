import { describe, expect, test } from 'vitest';
import { createClient } from '@supabase/supabase-js';

/**
 * Integration tests for local Supabase database connection
 * These tests validate that the testing framework can connect to the local database
 * and perform basic read-only operations as required for integration testing.
 */

const SUPABASE_URL = process.env.SUPABASE_URL || 'https://devdb.syncrobit.net';
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.ewogICJyb2xlIjogImFub24iLAogICJpc3MiOiAic3VwYWJhc2UiLAogICJpYXQiOiAxNzQxMDk1MDAwLAogICJleHAiOiAxODk4ODYxNDAwCn0.J1N7sT41EI4ORiRFvPm3FPDArs43dzEq9g9-iq7d3pY';

describe('Local Database Integration', () => {
  test('can connect to local Supabase instance', async () => {
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
      db: { schema: 'memory_master' }
    });

    // Test basic connection by querying a known table
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    expect(error).toBeNull();
    expect(data).toBeDefined();
  });

  test('can read from memory_master schema tables', async () => {
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
      db: { schema: 'memory_master' }
    });

    // Test reading from multiple tables to validate schema access
    const [usersResult, appsResult, memoriesResult] = await Promise.all([
      supabase.from('users').select('*', { count: 'exact', head: true }),
      supabase.from('apps').select('*', { count: 'exact', head: true }),
      supabase.from('memories').select('*', { count: 'exact', head: true })
    ]);

    expect(usersResult.error).toBeNull();
    expect(appsResult.error).toBeNull();
    expect(memoriesResult.error).toBeNull();

    expect(usersResult.count).toBeGreaterThan(0);
    expect(appsResult.count).toBeGreaterThan(0);
    expect(memoriesResult.count).toBeGreaterThan(0);
  });

  test('can validate JSON fields in database', async () => {
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
      db: { schema: 'memory_master' }
    });

    // Test that JSON fields are properly formatted
    const { data, error } = await supabase
      .from('memories')
      .select('id, metadata')
      .not('metadata', 'is', null)
      .limit(5);

    expect(error).toBeNull();
    expect(data).toBeDefined();
    expect(Array.isArray(data)).toBe(true);

    if (data && data.length > 0) {
      // Validate that metadata is valid JSON
      data.forEach(record => {
        expect(record.metadata).toBeDefined();
        expect(typeof record.metadata).toBe('object');
      });
    }
  });

  test('can validate referential integrity', async () => {
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
      db: { schema: 'memory_master' }
    });

    // Test that foreign key relationships are maintained
    const { data, error } = await supabase
      .from('memories')
      .select(`
        id,
        app_id,
        user_id,
        apps:apps!inner(id, name),
        users:users!inner(id, user_id)
      `)
      .limit(5);

    expect(error).toBeNull();
    expect(data).toBeDefined();
    expect(Array.isArray(data)).toBe(true);

    if (data && data.length > 0) {
      data.forEach(record => {
        expect(record.apps).toBeDefined();
        expect(record.users).toBeDefined();
        expect(record.app_id).toBeDefined();
        expect(record.user_id).toBeDefined();
      });
    }
  });

  test('database performance is acceptable', async () => {
    const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
      db: { schema: 'memory_master' }
    });

    const startTime = Date.now();

    // Test query performance
    const { data, error } = await supabase
      .from('memories')
      .select('id, content, created_at')
      .order('created_at', { ascending: false })
      .limit(10);

    const endTime = Date.now();
    const queryTime = endTime - startTime;

    expect(error).toBeNull();
    expect(data).toBeDefined();
    expect(queryTime).toBeLessThan(1000); // Query should complete within 1 second
  });
});
